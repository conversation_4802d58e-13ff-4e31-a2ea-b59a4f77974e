#!/usr/bin/env node

/**
 * 测试真实场景：模拟已存在压缩包的情况
 * 这个测试模拟您遇到的实际问题场景
 */

const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');

// 创建测试文件结构
async function createTestStructure() {
  const testDir = path.join(os.tmpdir(), 'real-scenario-test');
  const pwaDir = path.join(testDir, 'pwa');
  
  // 清理旧的测试目录
  try {
    await fs.rm(testDir, { recursive: true, force: true });
  } catch (error) {
    // 忽略删除错误
  }
  
  // 创建测试目录结构
  await fs.mkdir(pwaDir, { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder1'), { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder2'), { recursive: true });
  
  // 创建测试文件
  await fs.writeFile(path.join(pwaDir, 'file1.txt'), 'Content of file1 in root');
  await fs.writeFile(path.join(pwaDir, 'file2.txt'), 'Content of file2 in root');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file1.txt'), 'Content of file1 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file2.txt'), 'Content of file2 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder2', 'file1.txt'), 'Content of file1 in folder2');
  
  return {
    testDir,
    pwaDir,
    filePaths: [
      path.join(pwaDir, 'file1.txt'),
      path.join(pwaDir, 'file2.txt'),
      path.join(pwaDir, 'folder1', 'file1.txt'),
      path.join(pwaDir, 'folder1', 'file2.txt'),
      path.join(pwaDir, 'folder2', 'file1.txt')
    ]
  };
}

// 创建预存在的压缩包（模拟已有内容）
async function createPreExistingArchive(archivePath, workingDir, relativePaths) {
  return new Promise((resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    const args = ['a', '-t7z', '-mx0', '-y', archivePath, ...relativePaths];
    
    console.log(`📦 创建预存在的压缩包: ${path.basename(archivePath)}`);
    
    const process = spawn(sevenBin.path7za, args, { cwd: workingDir });
    let stdout = '';
    
    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ 预存在压缩包创建成功`);
        resolve({ stdout });
      } else {
        reject(new Error(`创建预存在压缩包失败`));
      }
    });
  });
}

// 执行7z压缩（模拟问题场景）
async function compressToExistingArchive(workingDir, relativePaths, archivePath, deleteFirst = false) {
  return new Promise(async (resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    // 如果需要，先删除压缩包
    if (deleteFirst) {
      try {
        await fs.unlink(archivePath);
        console.log(`🗑️ 删除已存在的压缩包`);
      } catch (error) {
        console.log(`📦 压缩包不存在`);
      }
    }

    const args = ['a', '-t7z', '-mx0', '-y', '-bb1', archivePath, ...relativePaths];
    
    console.log(`📦 执行压缩到: ${path.basename(archivePath)}`);
    console.log(`📦 删除旧文件: ${deleteFirst ? '是' : '否'}`);
    
    const process = spawn(sevenBin.path7za, args, { cwd: workingDir });
    let stdout = '';
    let stderr = '';
    
    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.stderr.on('data', (data) => { stderr += data.toString(); });
    
    process.on('close', (code) => {
      console.log(`📦 压缩完成，退出码: ${code}`);
      
      // 分析输出
      if (stdout.includes('Creating archive')) {
        console.log(`✅ 创建新压缩包`);
      } else if (stdout.includes('Updating archive')) {
        console.log(`⚠️ 更新现有压缩包 (这可能导致重复内容)`);
      } else if (stdout.includes('Open archive')) {
        console.log(`⚠️ 打开现有压缩包进行更新 (这可能导致重复内容)`);
      }
      
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`压缩失败: ${stderr}`));
      }
    });
  });
}

// 分析压缩包内容
async function analyzeArchiveDetailed(archivePath, testName) {
  return new Promise((resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    const process = spawn(sevenBin.path7za, ['l', archivePath]);
    let stdout = '';

    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`\n📦 ${testName} - 详细分析:`);
        
        const lines = stdout.split('\n');
        const fileLines = lines.filter(line => 
          line.includes('.txt') && !line.includes('Date') && !line.includes('----')
        );
        
        const paths = fileLines.map(line => {
          const parts = line.trim().split(/\s+/);
          return parts[parts.length - 1];
        }).filter(Boolean);
        
        console.log(`📄 总文件数: ${paths.length}`);
        console.log(`📄 文件列表:`);
        paths.forEach((p, i) => console.log(`   [${i+1}] ${p}`));
        
        // 详细分析重复
        const pathCounts = {};
        paths.forEach(p => {
          pathCounts[p] = (pathCounts[p] || 0) + 1;
        });
        
        const duplicates = Object.entries(pathCounts).filter(([path, count]) => count > 1);
        
        if (duplicates.length > 0) {
          console.log(`❌ 发现重复文件:`);
          duplicates.forEach(([path, count]) => {
            console.log(`   ${path} (出现 ${count} 次)`);
          });
        } else {
          console.log(`✅ 无重复文件`);
        }
        
        resolve({ 
          paths, 
          totalFiles: paths.length,
          duplicates: duplicates.length > 0 ? duplicates : null,
          hasDuplicates: duplicates.length > 0
        });
      } else {
        reject(new Error(`分析失败`));
      }
    });
  });
}

// 主测试函数
async function runRealScenarioTest() {
  console.log('🔍 开始真实场景测试（模拟已存在压缩包的情况）...\n');

  try {
    const { testDir, pwaDir, filePaths } = await createTestStructure();
    console.log(`📁 测试目录: ${testDir}`);
    
    const workingDir = pwaDir;
    const relativePaths = ['file1.txt', 'file2.txt', 'folder1/file1.txt', 'folder1/file2.txt', 'folder2/file1.txt'];
    const archivePath = path.join(testDir, 'pwa.7z');
    
    // 步骤1: 创建预存在的压缩包
    console.log(`\n📋 步骤1: 创建预存在的压缩包`);
    await createPreExistingArchive(archivePath, workingDir, relativePaths);
    const initialResult = await analyzeArchiveDetailed(archivePath, '初始压缩包');
    
    // 步骤2: 模拟问题场景 - 不删除旧文件直接压缩
    console.log(`\n📋 步骤2: 模拟问题场景（不删除旧文件）`);
    await compressToExistingArchive(workingDir, relativePaths, archivePath, false);
    const problemResult = await analyzeArchiveDetailed(archivePath, '问题场景结果');
    
    // 步骤3: 使用修复方案 - 删除旧文件再压缩
    console.log(`\n📋 步骤3: 使用修复方案（删除旧文件）`);
    await compressToExistingArchive(workingDir, relativePaths, archivePath, true);
    const fixedResult = await analyzeArchiveDetailed(archivePath, '修复后结果');
    
    // 总结
    console.log(`\n🎯 测试结果总结:`);
    console.log(`初始压缩包: ${initialResult.totalFiles} 个文件, ${initialResult.hasDuplicates ? '有重复' : '无重复'}`);
    console.log(`问题场景: ${problemResult.totalFiles} 个文件, ${problemResult.hasDuplicates ? '❌ 有重复' : '✅ 无重复'}`);
    console.log(`修复后: ${fixedResult.totalFiles} 个文件, ${fixedResult.hasDuplicates ? '❌ 有重复' : '✅ 无重复'}`);
    
    if (problemResult.totalFiles > initialResult.totalFiles) {
      console.log(`\n⚠️ 确认问题存在: 问题场景产生了 ${problemResult.totalFiles - initialResult.totalFiles} 个额外文件`);
    }
    
    if (!fixedResult.hasDuplicates && fixedResult.totalFiles === 5) {
      console.log(`\n🎉 修复验证成功: 删除旧文件的方案有效解决了重复问题`);
    } else {
      console.log(`\n❌ 修复可能不完整，需要进一步调查`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  runRealScenarioTest().catch(console.error);
}

module.exports = { runRealScenarioTest };
