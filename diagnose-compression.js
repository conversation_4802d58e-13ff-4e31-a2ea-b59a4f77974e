#!/usr/bin/env node

/**
 * 压缩功能深度诊断脚本
 * 创建测试文件结构，模拟压缩过程，分析可能的重复路径问题
 */

const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');

// 模拟问题场景的文件结构
async function createTestStructure() {
  const testDir = path.join(os.tmpdir(), 'compression-test');
  const pwaDir = path.join(testDir, 'pwa');
  
  // 清理旧的测试目录
  try {
    await fs.rm(testDir, { recursive: true, force: true });
  } catch (error) {
    // 忽略删除错误
  }
  
  // 创建测试目录结构
  await fs.mkdir(pwaDir, { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder1'), { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder2'), { recursive: true });
  
  // 创建测试文件
  await fs.writeFile(path.join(pwaDir, 'file1.txt'), 'Content of file1 in root');
  await fs.writeFile(path.join(pwaDir, 'file2.txt'), 'Content of file2 in root');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file1.txt'), 'Content of file1 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file2.txt'), 'Content of file2 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder2', 'file1.txt'), 'Content of file1 in folder2');
  
  console.log(`📁 创建测试目录结构: ${testDir}`);
  console.log(`📁 pwa目录: ${pwaDir}`);
  
  return {
    testDir,
    pwaDir,
    filePaths: [
      path.join(pwaDir, 'file1.txt'),
      path.join(pwaDir, 'file2.txt'),
      path.join(pwaDir, 'folder1', 'file1.txt'),
      path.join(pwaDir, 'folder1', 'file2.txt'),
      path.join(pwaDir, 'folder2', 'file1.txt')
    ]
  };
}

// 模拟修复后的路径计算逻辑
function findTrueCommonParent(normalizedPaths) {
  if (normalizedPaths.length === 0) {
    throw new Error("路径列表为空");
  }

  if (normalizedPaths.length === 1) {
    return path.dirname(normalizedPaths[0]);
  }

  // 将所有路径分割成组件
  const pathComponents = normalizedPaths.map(p => p.split(path.sep).filter(Boolean));
  
  // 找到最短路径的长度
  const minLength = Math.min(...pathComponents.map(components => components.length));
  
  // 找到公共前缀
  let commonLength = 0;
  for (let i = 0; i < minLength; i++) {
    const component = pathComponents[0][i];
    if (pathComponents.every(components => components[i] === component)) {
      commonLength = i + 1;
    } else {
      break;
    }
  }
  
  if (commonLength === 0) {
    // 没有公共前缀，使用根目录
    return path.sep;
  }
  
  // 构建公共父目录路径
  const commonComponents = pathComponents[0].slice(0, commonLength);
  const commonParent = path.sep + path.join(...commonComponents);
  
  return commonParent;
}

function calculateRelativePaths(sourcePaths) {
  if (sourcePaths.length === 0) {
    throw new Error("源路径列表为空");
  }

  if (sourcePaths.length === 1) {
    const singlePath = sourcePaths[0];
    const parentDir = path.dirname(singlePath);
    const relativePath = path.basename(singlePath);
    return {
      workingDir: parentDir,
      relativePaths: [relativePath],
    };
  }

  // 多个路径的情况，找到真正的公共父目录
  const normalizedPaths = sourcePaths.map((p) => path.resolve(p));
  let commonParent = findTrueCommonParent(normalizedPaths);
  
  // 计算相对路径
  const relativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

  // 验证相对路径的正确性
  const hasEmptyRelativePath = relativePaths.some(rp => rp === '' || rp === '.');
  if (hasEmptyRelativePath) {
    commonParent = path.dirname(commonParent);
    const adjustedRelativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));
    
    return {
      workingDir: commonParent,
      relativePaths: adjustedRelativePaths,
    };
  }

  return {
    workingDir: commonParent,
    relativePaths,
  };
}

// 执行7z命令测试
async function test7zCommand(workingDir, relativePaths, outputPath) {
  return new Promise((resolve, reject) => {
    // 检查7zip-bin是否可用
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      console.log(`❌ 7zip-bin不可用: ${error}`);
      reject(error);
      return;
    }

    const args = [
      'a', // 添加到压缩包
      '-t7z', // 7z格式
      '-mx0', // 仅存储，不压缩
      '-y', // 自动回答yes
      '-bb1', // 设置输出日志级别
      outputPath,
      ...relativePaths,
    ];

    console.log(`\n🔍 测试7z命令:`);
    console.log(`📦 工作目录: ${workingDir}`);
    console.log(`📦 输出文件: ${outputPath}`);
    console.log(`📦 相对路径: ${relativePaths.join(', ')}`);
    console.log(`📦 完整命令: cd "${workingDir}" && "${sevenBin.path7za}" ${args.join(' ')}`);

    const process = spawn(sevenBin.path7za, args, {
      cwd: workingDir,
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      console.log(`📦 7z进程结束，退出码: ${code}`);
      if (stdout) console.log(`📦 stdout: ${stdout}`);
      if (stderr) console.log(`📦 stderr: ${stderr}`);

      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`7z失败，退出码: ${code}, 错误: ${stderr}`));
      }
    });

    process.on('error', (error) => {
      reject(error);
    });
  });
}

// 分析压缩包内容
async function analyzeArchive(archivePath) {
  return new Promise((resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    const args = ['l', archivePath]; // 列出压缩包内容

    const process = spawn(sevenBin.path7za, args);
    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        console.log(`\n📦 压缩包内容分析:`);
        console.log(stdout);
        
        // 分析是否有重复路径
        const lines = stdout.split('\n');
        const fileLines = lines.filter(line => 
          line.includes('.txt') && !line.includes('Date') && !line.includes('----')
        );
        
        console.log(`\n🔍 文件列表分析:`);
        fileLines.forEach(line => {
          console.log(`   ${line.trim()}`);
        });
        
        // 检查重复路径
        const paths = fileLines.map(line => {
          const parts = line.trim().split(/\s+/);
          return parts[parts.length - 1]; // 最后一部分是文件路径
        }).filter(Boolean);
        
        console.log(`\n🔍 提取的文件路径:`);
        paths.forEach(p => console.log(`   ${p}`));
        
        // 检查是否有重复的路径模式
        const duplicates = paths.filter((path, index) => 
          paths.findIndex(p => p.endsWith(path.split('/').pop())) !== index
        );
        
        if (duplicates.length > 0) {
          console.log(`⚠️ 检测到可能的重复路径:`);
          duplicates.forEach(d => console.log(`   ${d}`));
        } else {
          console.log(`✅ 未检测到明显的重复路径`);
        }
        
        resolve({ stdout, stderr, paths });
      } else {
        reject(new Error(`分析失败，退出码: ${code}, 错误: ${stderr}`));
      }
    });
  });
}

// 主测试函数
async function runDiagnosis() {
  console.log('🔍 开始压缩功能深度诊断...\n');

  try {
    // 1. 创建测试文件结构
    const { testDir, pwaDir, filePaths } = await createTestStructure();
    
    // 2. 测试路径计算
    console.log(`\n📋 测试路径计算:`);
    console.log(`源路径:`, filePaths);
    
    const { workingDir, relativePaths } = calculateRelativePaths(filePaths);
    console.log(`工作目录: ${workingDir}`);
    console.log(`相对路径:`, relativePaths);
    
    // 3. 验证文件存在性
    console.log(`\n📋 验证文件存在性:`);
    for (const relativePath of relativePaths) {
      const fullPath = path.join(workingDir, relativePath);
      try {
        const stats = await fs.stat(fullPath);
        console.log(`✅ ${relativePath} -> ${stats.isFile() ? '文件' : '目录'}`);
      } catch (error) {
        console.log(`❌ ${relativePath} -> 不存在`);
      }
    }
    
    // 4. 执行7z压缩测试
    const outputPath = path.join(testDir, 'test-archive.7z');
    console.log(`\n📋 执行7z压缩测试:`);
    
    try {
      await test7zCommand(workingDir, relativePaths, outputPath);
      console.log(`✅ 压缩成功: ${outputPath}`);
      
      // 5. 分析压缩包内容
      await analyzeArchive(outputPath);
      
    } catch (error) {
      console.log(`❌ 压缩失败: ${error}`);
    }
    
    console.log(`\n🎉 诊断完成！`);
    console.log(`测试文件保留在: ${testDir}`);
    
  } catch (error) {
    console.error('❌ 诊断失败:', error);
  }
}

// 运行诊断
if (require.main === module) {
  runDiagnosis().catch(console.error);
}

module.exports = { runDiagnosis, calculateRelativePaths, createTestStructure };
