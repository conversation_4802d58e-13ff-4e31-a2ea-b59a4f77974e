#!/usr/bin/env node

/**
 * 测试重复压缩场景，验证修复效果
 * 模拟多次压缩同一个文件夹，确保不会产生重复路径
 */

const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');

// 创建测试文件结构
async function createTestStructure() {
  const testDir = path.join(os.tmpdir(), 'duplicate-compression-test');
  const pwaDir = path.join(testDir, 'pwa');
  
  // 清理旧的测试目录
  try {
    await fs.rm(testDir, { recursive: true, force: true });
  } catch (error) {
    // 忽略删除错误
  }
  
  // 创建测试目录结构
  await fs.mkdir(pwaDir, { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder1'), { recursive: true });
  await fs.mkdir(path.join(pwaDir, 'folder2'), { recursive: true });
  
  // 创建测试文件
  await fs.writeFile(path.join(pwaDir, 'file1.txt'), 'Content of file1 in root');
  await fs.writeFile(path.join(pwaDir, 'file2.txt'), 'Content of file2 in root');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file1.txt'), 'Content of file1 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder1', 'file2.txt'), 'Content of file2 in folder1');
  await fs.writeFile(path.join(pwaDir, 'folder2', 'file1.txt'), 'Content of file1 in folder2');
  
  return {
    testDir,
    pwaDir,
    filePaths: [
      path.join(pwaDir, 'file1.txt'),
      path.join(pwaDir, 'file2.txt'),
      path.join(pwaDir, 'folder1', 'file1.txt'),
      path.join(pwaDir, 'folder1', 'file2.txt'),
      path.join(pwaDir, 'folder2', 'file1.txt')
    ]
  };
}

// 执行7z压缩（修复前的方式 - 不删除旧文件）
async function compressWithoutCleanup(workingDir, relativePaths, outputPath, testName) {
  return new Promise((resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    const args = [
      'a', '-t7z', '-mx0', '-y', '-bb1',
      outputPath,
      ...relativePaths,
    ];

    console.log(`\n🔍 ${testName}:`);
    console.log(`📦 命令: cd "${workingDir}" && "${sevenBin.path7za}" ${args.join(' ')}`);

    const process = spawn(sevenBin.path7za, args, { cwd: workingDir });
    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.stderr.on('data', (data) => { stderr += data.toString(); });

    process.on('close', (code) => {
      console.log(`📦 退出码: ${code}`);
      
      // 检查是否是创建还是更新
      if (stdout.includes('Creating archive')) {
        console.log(`✅ 创建新压缩包`);
      } else if (stdout.includes('Updating archive')) {
        console.log(`⚠️ 更新现有压缩包 (可能导致重复)`);
      }
      
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`压缩失败: ${stderr}`));
      }
    });
  });
}

// 执行7z压缩（修复后的方式 - 删除旧文件）
async function compressWithCleanup(workingDir, relativePaths, outputPath, testName) {
  return new Promise(async (resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    // 删除已存在的压缩包
    try {
      await fs.unlink(outputPath);
      console.log(`🗑️ 删除已存在的压缩包`);
    } catch (error) {
      console.log(`📦 压缩包不存在，将创建新文件`);
    }

    const args = [
      'a', '-t7z', '-mx0', '-y', '-bb1', '-ms=off', '-mmt=on',
      outputPath,
      ...relativePaths,
    ];

    console.log(`\n🔍 ${testName}:`);
    console.log(`📦 命令: cd "${workingDir}" && "${sevenBin.path7za}" ${args.join(' ')}`);

    const process = spawn(sevenBin.path7za, args, { cwd: workingDir });
    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.stderr.on('data', (data) => { stderr += data.toString(); });

    process.on('close', (code) => {
      console.log(`📦 退出码: ${code}`);
      
      // 检查是否是创建还是更新
      if (stdout.includes('Creating archive')) {
        console.log(`✅ 创建新压缩包`);
      } else if (stdout.includes('Updating archive')) {
        console.log(`⚠️ 更新现有压缩包 (可能导致重复)`);
      }
      
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`压缩失败: ${stderr}`));
      }
    });
  });
}

// 分析压缩包内容
async function analyzeArchive(archivePath, testName) {
  return new Promise((resolve, reject) => {
    let sevenBin;
    try {
      sevenBin = require('7zip-bin');
    } catch (error) {
      reject(error);
      return;
    }

    const process = spawn(sevenBin.path7za, ['l', archivePath]);
    let stdout = '';

    process.stdout.on('data', (data) => { stdout += data.toString(); });
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`\n📦 ${testName} - 压缩包内容分析:`);
        
        const lines = stdout.split('\n');
        const fileLines = lines.filter(line => 
          line.includes('.txt') && !line.includes('Date') && !line.includes('----')
        );
        
        const paths = fileLines.map(line => {
          const parts = line.trim().split(/\s+/);
          return parts[parts.length - 1];
        }).filter(Boolean);
        
        console.log(`📄 文件数量: ${paths.length}`);
        console.log(`📄 文件列表:`);
        paths.forEach(p => console.log(`   ${p}`));
        
        // 检查重复
        const uniquePaths = [...new Set(paths)];
        if (paths.length !== uniquePaths.length) {
          console.log(`❌ 检测到重复文件: ${paths.length - uniquePaths.length} 个重复`);
        } else {
          console.log(`✅ 无重复文件`);
        }
        
        resolve({ paths, hasDuplicates: paths.length !== uniquePaths.length });
      } else {
        reject(new Error(`分析失败`));
      }
    });
  });
}

// 主测试函数
async function runDuplicateCompressionTest() {
  console.log('🔍 开始重复压缩测试...\n');

  try {
    const { testDir, pwaDir, filePaths } = await createTestStructure();
    console.log(`📁 测试目录: ${testDir}`);
    
    const workingDir = pwaDir;
    const relativePaths = ['file1.txt', 'file2.txt', 'folder1/file1.txt', 'folder1/file2.txt', 'folder2/file1.txt'];
    
    // 测试1: 修复前的方式（不删除旧文件）
    const oldWayPath = path.join(testDir, 'old-way.7z');
    
    console.log(`\n📋 测试1: 修复前的方式（可能产生重复）`);
    await compressWithoutCleanup(workingDir, relativePaths, oldWayPath, '第一次压缩');
    await compressWithoutCleanup(workingDir, relativePaths, oldWayPath, '第二次压缩（更新模式）');
    
    const oldWayResult = await analyzeArchive(oldWayPath, '修复前方式');
    
    // 测试2: 修复后的方式（删除旧文件）
    const newWayPath = path.join(testDir, 'new-way.7z');
    
    console.log(`\n📋 测试2: 修复后的方式（应该无重复）`);
    await compressWithCleanup(workingDir, relativePaths, newWayPath, '第一次压缩');
    await compressWithCleanup(workingDir, relativePaths, newWayPath, '第二次压缩（重新创建）');
    
    const newWayResult = await analyzeArchive(newWayPath, '修复后方式');
    
    // 总结
    console.log(`\n🎯 测试结果总结:`);
    console.log(`修复前方式: ${oldWayResult.hasDuplicates ? '❌ 有重复' : '✅ 无重复'} (${oldWayResult.paths.length} 个文件)`);
    console.log(`修复后方式: ${newWayResult.hasDuplicates ? '❌ 有重复' : '✅ 无重复'} (${newWayResult.paths.length} 个文件)`);
    
    if (!newWayResult.hasDuplicates && newWayResult.paths.length === 5) {
      console.log(`🎉 修复成功！压缩功能现在能正确处理重复压缩场景。`);
    } else {
      console.log(`❌ 修复可能不完整，需要进一步调查。`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  runDuplicateCompressionTest().catch(console.error);
}

module.exports = { runDuplicateCompressionTest };
