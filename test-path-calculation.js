#!/usr/bin/env node

/**
 * 测试路径计算逻辑的修复效果
 * 模拟不同的文件路径场景，验证公共父目录和相对路径的计算是否正确
 */

const path = require('path');

// 模拟修复后的路径计算逻辑
function findTrueCommonParent(normalizedPaths) {
  if (normalizedPaths.length === 0) {
    throw new Error("路径列表为空");
  }

  if (normalizedPaths.length === 1) {
    return path.dirname(normalizedPaths[0]);
  }

  // 将所有路径分割成组件
  const pathComponents = normalizedPaths.map(p => p.split(path.sep).filter(Boolean));
  
  // 找到最短路径的长度
  const minLength = Math.min(...pathComponents.map(components => components.length));
  
  // 找到公共前缀
  let commonLength = 0;
  for (let i = 0; i < minLength; i++) {
    const component = pathComponents[0][i];
    if (pathComponents.every(components => components[i] === component)) {
      commonLength = i + 1;
    } else {
      break;
    }
  }
  
  if (commonLength === 0) {
    // 没有公共前缀，使用根目录
    return path.sep;
  }
  
  // 构建公共父目录路径
  const commonComponents = pathComponents[0].slice(0, commonLength);
  const commonParent = path.sep + path.join(...commonComponents);
  
  console.log(`📦 公共前缀长度: ${commonLength}, 公共父目录: ${commonParent}`);
  
  return commonParent;
}

function calculateRelativePaths(sourcePaths) {
  if (sourcePaths.length === 0) {
    throw new Error("源路径列表为空");
  }

  if (sourcePaths.length === 1) {
    // 单个路径的情况
    const singlePath = sourcePaths[0];
    const parentDir = path.dirname(singlePath);
    const relativePath = path.basename(singlePath);
    console.log(`📦 单个路径处理: 工作目录=${parentDir}, 相对路径=${relativePath}`);
    return {
      workingDir: parentDir,
      relativePaths: [relativePath],
    };
  }

  // 多个路径的情况，找到真正的公共父目录
  const normalizedPaths = sourcePaths.map((p) => path.resolve(p));
  console.log(`📦 标准化路径:`, normalizedPaths);

  // 改进的公共父目录查找算法
  let commonParent = findTrueCommonParent(normalizedPaths);
  
  // 计算相对路径
  const relativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

  // 验证相对路径的正确性
  const hasEmptyRelativePath = relativePaths.some(rp => rp === '' || rp === '.');
  if (hasEmptyRelativePath) {
    // 如果有空的相对路径，说明公共父目录设置过深，需要向上调整
    console.log(`📦 检测到空相对路径，调整公共父目录`);
    commonParent = path.dirname(commonParent);
    const adjustedRelativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));
    
    console.log(`📦 调整后的公共父目录: ${commonParent}`);
    console.log(`📦 调整后的相对路径:`, adjustedRelativePaths);
    
    return {
      workingDir: commonParent,
      relativePaths: adjustedRelativePaths,
    };
  }

  console.log(`📦 计算相对路径: 公共父目录=${commonParent}`);
  console.log(
    `📦 相对路径映射:`,
    normalizedPaths.map((abs, i) => `${abs} -> ${relativePaths[i]}`)
  );

  return {
    workingDir: commonParent,
    relativePaths,
  };
}

// 测试用例
function runTests() {
  console.log('🧪 开始测试路径计算修复...\n');

  // 测试用例1: 问题场景 - pwa文件夹结构
  console.log('📋 测试用例1: pwa文件夹结构');
  const testCase1 = [
    '/Users/<USER>/Downloads/pwa/folder1/file1.txt',
    '/Users/<USER>/Downloads/pwa/folder1/file2.txt',
    '/Users/<USER>/Downloads/pwa/folder2/file1.txt',
    '/Users/<USER>/Downloads/pwa/file1.txt',
    '/Users/<USER>/Downloads/pwa/file2.txt'
  ];
  
  const result1 = calculateRelativePaths(testCase1);
  console.log('✅ 预期工作目录: /Users/<USER>/Downloads');
  console.log('✅ 预期相对路径: pwa/folder1/file1.txt, pwa/folder1/file2.txt, ...');
  console.log('🔍 实际结果:', result1);
  console.log('\n');

  // 测试用例2: 单个文件
  console.log('📋 测试用例2: 单个文件');
  const testCase2 = ['/Users/<USER>/Downloads/pwa/file.txt'];
  const result2 = calculateRelativePaths(testCase2);
  console.log('✅ 预期工作目录: /Users/<USER>/Downloads/pwa');
  console.log('✅ 预期相对路径: file.txt');
  console.log('🔍 实际结果:', result2);
  console.log('\n');

  // 测试用例3: 不同层级的文件
  console.log('📋 测试用例3: 不同层级的文件');
  const testCase3 = [
    '/Users/<USER>/Downloads/project/src/file1.js',
    '/Users/<USER>/Downloads/project/package.json',
    '/Users/<USER>/Downloads/project/docs/readme.md'
  ];
  const result3 = calculateRelativePaths(testCase3);
  console.log('✅ 预期工作目录: /Users/<USER>/Downloads');
  console.log('✅ 预期相对路径: project/src/file1.js, project/package.json, project/docs/readme.md');
  console.log('🔍 实际结果:', result3);
  console.log('\n');

  console.log('🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { calculateRelativePaths, findTrueCommonParent };
